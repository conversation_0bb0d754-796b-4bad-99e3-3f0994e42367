const path = require('path');
const dotenv = require('dotenv');

// 加载根目录下的多种环境文件（按优先级覆盖）
const rootDir = path.resolve(__dirname, '../../');
[ '.env', '.env.development', '.env.local', '.env.development.local' ].forEach((name) => {
  dotenv.config({ path: path.join(rootDir, name) });
});

module.exports = ({ config }) => {
  const apiBaseUrl = process.env.API_BASE_URL || process.env.EXPO_PUBLIC_API_BASE_URL || 'http://192.168.2.162:8080';
  const mediaBaseUrl = process.env.MEDIA_BASE_URL || process.env.EXPO_PUBLIC_MEDIA_BASE_URL || 'http://192.168.2.162:3000';
  const nodeEnv = process.env.NODE_ENV || 'development';
  const debugMode = String(process.env.DEBUG_MODE ?? (nodeEnv !== 'production'));

  return {
    ...config,
    extra: {
      ...(config.extra || {}),
      apiBaseUrl,
      mediaBaseUrl,
      nodeEnv,
      debugMode,
    },
  };
}; 