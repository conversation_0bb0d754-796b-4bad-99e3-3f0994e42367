#!/usr/bin/env node

/**
 * Start Expo with Mac host IP for OrbStack environment
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Read Mac IP from config file
function getMacIPFromConfig() {
  try {
    const configPath = path.join(__dirname, '../src/config/config.js');
    const configContent = fs.readFileSync(configPath, 'utf8');
    const match = configContent.match(/MAC_HOST_IP = '([^']+)'/);
    return match ? match[1] : null;
  } catch (error) {
    console.error('Error reading config file:', error.message);
    return null;
  }
}

function main() {
  const macIP = getMacIPFromConfig();
  
  if (!macIP) {
    console.error('❌ Could not find MAC_HOST_IP in config file');
    console.log('Please update src/config/config.js with your Mac WiFi IP address');
    process.exit(1);
  }

  console.log(`🍎 Starting Expo with Mac IP: ${macIP}`);
  console.log('📱 This will allow iPhone to connect properly\n');

  // Set environment variables and start Expo
  const env = {
    ...process.env,
    EXPO_DEVTOOLS_LISTEN_ADDRESS: '0.0.0.0',
    REACT_NATIVE_PACKAGER_HOSTNAME: macIP,
  };

  const args = [
    'start',
    '--host', 'lan',  // Use 'lan' instead of specific IP
    '--port', '8081'
  ];

  console.log(`Running: expo ${args.join(' ')}`);
  console.log(`Environment: REACT_NATIVE_PACKAGER_HOSTNAME=${macIP}\n`);

  // Spawn expo process
  const expoProcess = spawn('npx', ['expo', ...args], {
    env,
    stdio: 'inherit',
    cwd: process.cwd()
  });

  expoProcess.on('error', (error) => {
    console.error('Failed to start Expo:', error.message);
    process.exit(1);
  });

  expoProcess.on('close', (code) => {
    console.log(`Expo process exited with code ${code}`);
    process.exit(code);
  });

  // Handle Ctrl+C
  process.on('SIGINT', () => {
    console.log('\n🛑 Stopping Expo...');
    expoProcess.kill('SIGINT');
  });
}

main();
