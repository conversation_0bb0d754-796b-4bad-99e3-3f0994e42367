#!/bin/bash

echo "🍎 iOS 测试准备脚本"
echo "===================="

# 获取本机 IP 地址
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    IP=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | head -n1 | awk '{print $2}')
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    IP=$(hostname -I | awk '{print $1}')
else
    # Windows (Git Bash)
    IP=$(ipconfig | grep "IPv4" | head -n1 | awk '{print $NF}')
fi

echo "📱 检测到的 IP 地址: $IP"
echo ""

# 检查配置文件
CONFIG_FILE="src/config/config.js"
if [ -f "$CONFIG_FILE" ]; then
    CURRENT_IP=$(grep "LOCAL_IP = " $CONFIG_FILE | sed "s/.*'\(.*\)'.*/\1/")
    echo "📋 当前配置的 IP: $CURRENT_IP"
    
    if [ "$IP" != "$CURRENT_IP" ]; then
        echo "⚠️  IP 地址不匹配！"
        echo "请手动更新 $CONFIG_FILE 中的 LOCAL_IP 为: $IP"
    else
        echo "✅ IP 地址配置正确"
    fi
else
    echo "❌ 配置文件不存在: $CONFIG_FILE"
fi

echo ""
echo "🔧 测试步骤："
echo "1. 确保手机和电脑在同一 WiFi 网络"
echo "2. 启动 m1 v2 服务器:"
echo "   cd ../frontend_m1_player_v2"
echo "   npm run dev -- --host 0.0.0.0"
echo ""
echo "3. 检查连接:"
echo "   npm run check-connection"
echo ""
echo "4. 启动 React Native:"
echo "   npm start"
echo ""
echo "5. 在手机上打开 Expo Go，扫描二维码"
echo ""
echo "📱 测试 URL: http://$IP:5173"
echo "🔗 Expo URL: exp://$IP:8081"
echo ""
echo "💡 如果遇到问题，请检查："
echo "   - 防火墙设置"
echo "   - WiFi 网络连接"
echo "   - IP 地址配置"
