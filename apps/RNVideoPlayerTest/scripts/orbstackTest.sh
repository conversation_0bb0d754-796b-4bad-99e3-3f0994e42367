#!/bin/bash

echo "🐳 OrbStack Linux VM iPhone 测试指南"
echo "=================================="

# 获取虚拟机 IP
VM_IP=$(hostname -I | awk '{print $1}')
echo "🖥️  虚拟机 IP: $VM_IP"

echo ""
echo "📋 测试步骤："
echo ""

echo "1️⃣ 在 Mac 主机上获取 WiFi IP 地址:"
echo "   在 Mac 终端中运行:"
echo "   ifconfig | grep 'inet ' | grep -v 127.0.0.1"
echo ""

echo "2️⃣ 更新配置文件:"
echo "   编辑 src/config/config.js"
echo "   将 MAC_HOST_IP 设置为你的 Mac WiFi IP 地址"
echo ""

echo "3️⃣ 启动 m1 v2 服务器 (在虚拟机中):"
echo "   cd apps/frontend_m1_player_v2"
echo "   npm run dev -- --host 0.0.0.0 --port 5173"
echo ""

echo "4️⃣ 检查端口转发:"
echo "   OrbStack 应该自动将虚拟机的 5173 端口转发到 Mac"
echo "   在 Mac 浏览器中测试: http://localhost:5173"
echo ""

echo "5️⃣ 启动 React Native (在虚拟机中):"
echo "   cd apps/RNVideoPlayerTest"
echo "   EXPO_DEVTOOLS_LISTEN_ADDRESS=0.0.0.0 npm start"
echo ""

echo "6️⃣ iPhone 连接:"
echo "   - 确保 iPhone 和 Mac 在同一 WiFi 网络"
echo "   - 在 iPhone 上安装 Expo Go"
echo "   - 扫描二维码或手动输入 URL"
echo ""

echo "🔧 故障排除:"
echo ""
echo "如果 iPhone 无法连接:"
echo "1. 检查 Mac 防火墙设置"
echo "2. 确认 OrbStack 端口转发正常工作"
echo "3. 在 Mac 浏览器中测试 http://localhost:5173"
echo "4. 在 iPhone Safari 中测试 http://[Mac-IP]:5173"
echo ""

echo "💡 OrbStack 特殊说明:"
echo "- OrbStack 会自动处理端口转发"
echo "- 虚拟机中的服务需要绑定到 0.0.0.0"
echo "- iPhone 通过 Mac 的 IP 地址访问服务"
echo ""

echo "🌐 测试 URL:"
echo "- M1 V2: http://localhost:5173 (在 Mac 上测试)"
echo "- M1 V2: http://[Mac-IP]:5173 (在 iPhone 上测试)"
echo "- Expo: exp://[Mac-IP]:8081 (Expo Go 连接)"
