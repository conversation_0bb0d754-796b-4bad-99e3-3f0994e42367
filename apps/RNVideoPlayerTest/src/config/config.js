import Constants from 'expo-constants';

/**
 * Configuration file for the React Native Video Player Test app
 */

// 优先从 Expo extra 注入的配置读取；若缺失，回退到开发默认值以避免破坏现有功能
const API_BASE_URL = (Constants?.expoConfig?.extra?.apiBaseUrl) || 'http://*************:8080';
const MEDIA_BASE_URL = (Constants?.expoConfig?.extra?.mediaBaseUrl) || 'http://*************:3000';

export const API_CONFIG = {
  BASE_URL: API_BASE_URL,
  MEDIA_BASE_URL: MEDIA_BASE_URL,
  TIMEOUT: 10000,
};

// M1 V2 Player configuration（基于后端主机名派生 LAN 地址，避免重复配置）
function resolveHostFromUrl(url) {
  try {
    return new URL(url).hostname;
  } catch {
    return 'localhost';
  }
}
const LAN_HOST = resolveHostFromUrl(API_BASE_URL);

export const M1_V2_CONFIG = {
  DEV_LOCALHOST: 'http://localhost:5174',
  DEV_LAN: `http://${LAN_HOST}:5174`,
  PRODUCTION: 'https://your-production-url.com',
  get URL() {
    if (__DEV__) {
      return this.DEV_LAN;
    }
    return this.PRODUCTION;
  },
};

// Network configuration
export const NETWORK_CONFIG = {
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};

// App configuration
export const APP_CONFIG = {
  DEFAULT_LANGUAGE: 'en',
  SUPPORTED_LANGUAGES: ['en', 'zh'],
  CONTROLS_AUTO_HIDE_TIMEOUT: 4000,
  VIDEO_PLAYER: {
    HARDWARE_ACCELERATION: true,
    ALLOW_INLINE_PLAYBACK: true,
    REQUIRE_USER_ACTION: false,
  },
};

// Debug configuration
export const DEBUG_CONFIG = {
  ENABLE_LOGGING: __DEV__,
  ENABLE_WEBVIEW_DEBUG: __DEV__,
  SHOW_NETWORK_LOGS: __DEV__,
};

export const getIPInstructions = () => {
  return `\nUpdate API_BASE_URL and MEDIA_BASE_URL in the repository root .env to change endpoints for both RN and M1V2.\n`;
};

export default {
  M1_V2_CONFIG,
  NETWORK_CONFIG,
  APP_CONFIG,
  DEBUG_CONFIG,
  getIPInstructions,
};
