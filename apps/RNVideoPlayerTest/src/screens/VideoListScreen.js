import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  Alert,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { authService } from '../services/AuthService';
import { videoStore } from '../stores/VideoStore';

const VideoListScreen = ({ navigation }) => {
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [videos, setVideos] = useState([]);

  // 初始化应用
  useEffect(() => {
    initializeApp();
  }, []);

  // 监听语言变化
  useEffect(() => {
    videoStore.setLocale(currentLanguage);
  }, [currentLanguage]);

  // 监听VideoStore状态变化
  useEffect(() => {
    const handleStoreChange = (event, data) => {
      switch (event) {
        case 'loadingStarted':
          if (data.refresh) {
            setRefreshing(true);
          }
          break;
        case 'loadingFinished':
          setLoading(false);
          setRefreshing(false);
          break;
        case 'loadingError':
          setError(data.message);
          setLoading(false);
          setRefreshing(false);
          break;
        case 'feedLoaded':
          setError(null);
          // 更新videos状态以触发重新渲染
          setVideos([...videoStore.videos]);
          break;
        case 'userStatesLoaded':
          // 用户状态加载完成后也需要更新UI
          setVideos([...videoStore.videos]);
          break;
      }
    };

    videoStore.addListener(handleStoreChange);

    // 设置初始videos状态
    setVideos([...videoStore.videos]);

    return () => videoStore.removeListener(handleStoreChange);
  }, []);

  const initializeApp = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('VideoListScreen: Initializing app...');

      // 1. 认证
      await authService.authenticate('admin');
      console.log('VideoListScreen: Authentication successful');

      // 2. 加载视频数据
      await videoStore.loadFeed(1, true);
      console.log('VideoListScreen: Feed loaded successfully');

    } catch (error) {
      console.error('VideoListScreen: Initialization failed:', error);
      setError(error.message);
      Alert.alert('初始化失败', error.message, [
        { text: '重试', onPress: initializeApp },
        { text: '取消', style: 'cancel' }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const toggleLanguage = () => {
    const newLanguage = currentLanguage === 'en' ? 'zh' : 'en';
    setCurrentLanguage(newLanguage);
  };

  const handleVideoPress = (video, index) => {
    // 检查预加载
    videoStore.checkPreload(index);

    navigation.navigate('VideoPlayer', {
      videoIndex: index,
      allVideos: videoStore.getVideosForLegacyFormat(), // 兼容现有格式
      currentLanguage
    });
  };

  const handleRefresh = useCallback(async () => {
    try {
      await videoStore.refresh();
    } catch (error) {
      Alert.alert('刷新失败', error.message);
    }
  }, []);

  const handleLoadMore = useCallback(async () => {
    if (videoStore.hasMorePages() && !videoStore.loading) {
      try {
        await videoStore.loadNextPage();
      } catch (error) {
        console.warn('Load more failed:', error);
      }
    }
  }, []);

  const getLocalizedText = (textObj) => {
    if (typeof textObj === 'object' && textObj !== null) {
      return textObj[currentLanguage] || textObj.en || textObj.zh || 'No text';
    }
    return textObj || 'No text';
  };

  const renderVideoCard = ({ item, index }) => {
    const formattedStats = item.getFormattedStats ? item.getFormattedStats() : item.stats;

    return (
      <TouchableOpacity
        style={styles.card}
        onPress={() => handleVideoPress(item, index)}
        activeOpacity={0.7}
      >
        <Image
          source={{ uri: item.coverImage }}
          style={styles.coverImage}
          resizeMode="cover"
        />
        <View style={styles.cardContent}>
          <Text style={styles.title} numberOfLines={2}>
            {item.getLocalizedText ? item.getLocalizedText('title') : getLocalizedText(item.title)}
          </Text>
          <Text style={styles.description} numberOfLines={3}>
            {item.getLocalizedText ? item.getLocalizedText('description') : getLocalizedText(item.description)}
          </Text>
          <View style={styles.statsContainer}>
            <Text style={styles.stats}>👍 {formattedStats.likes}</Text>
            <Text style={styles.stats}>⭐ {formattedStats.collections}</Text>
            <Text style={styles.stats}>👁 {formattedStats.views}</Text>
          </View>
          {/* 显示视频时长 */}
          <View style={styles.durationContainer}>
            <Text style={styles.duration}>
              {item.getFormattedDuration ? item.getFormattedDuration() : '0:00'}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderLoadingScreen = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color="#007AFF" />
      <Text style={styles.loadingText}>
        {currentLanguage === 'zh' ? '加载中...' : 'Loading...'}
      </Text>
    </View>
  );

  const renderErrorScreen = () => (
    <View style={styles.errorContainer}>
      <Text style={styles.errorText}>
        {error || (currentLanguage === 'zh' ? '加载失败' : 'Loading failed')}
      </Text>
      <TouchableOpacity style={styles.retryButton} onPress={initializeApp}>
        <Text style={styles.retryButtonText}>
          {currentLanguage === 'zh' ? '重试' : 'Retry'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderFooter = () => {
    if (!videoStore.hasMorePages()) {
      return (
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>
            {currentLanguage === 'zh' ? '没有更多视频了' : 'No more videos'}
          </Text>
        </View>
      );
    }

    if (videoStore.loading) {
      return (
        <View style={styles.footerContainer}>
          <ActivityIndicator size="small" color="#007AFF" />
          <Text style={styles.footerText}>
            {currentLanguage === 'zh' ? '加载更多...' : 'Loading more...'}
          </Text>
        </View>
      );
    }

    return null;
  };

  // 如果正在初始化，显示加载屏幕
  if (loading && videos.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#fff" />
        {renderLoadingScreen()}
      </SafeAreaView>
    );
  }

  // 如果有错误且没有数据，显示错误屏幕
  if (error && videos.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#fff" />
        {renderErrorScreen()}
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {currentLanguage === 'zh' ? '视频列表' : 'Video List'}
        </Text>
        <TouchableOpacity
          style={styles.languageButton}
          onPress={toggleLanguage}
        >
          <Text style={styles.languageButtonText}>
            {currentLanguage === 'en' ? '中文' : 'EN'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Video List */}
      <FlatList
        data={videos}
        renderItem={renderVideoCard}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#007AFF']}
            tintColor="#007AFF"
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        ListFooterComponent={renderFooter}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  languageButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#007AFF',
    borderRadius: 16,
  },
  languageButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  listContainer: {
    padding: 16,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  coverImage: {
    width: '100%',
    height: 200,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  cardContent: {
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 12,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  stats: {
    fontSize: 14,
    color: '#999',
  },
  durationContainer: {
    alignItems: 'flex-end',
  },
  duration: {
    fontSize: 12,
    color: '#666',
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorText: {
    fontSize: 16,
    color: '#ff3b30',
    textAlign: 'center',
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  footerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  footerText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#666',
  },
});

export default VideoListScreen;
