{"name": "rnvideoplayertest", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "start-orbstack": "expo start --host lan", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "check-connection": "node scripts/checkConnection.js", "check-orbstack": "node scripts/checkOrbstackPorts.js", "orbstack-guide": "bash scripts/orbstackTest.sh"}, "dependencies": {"@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "expo": "~53.0.17", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.27.1", "react-native-safe-area-context": "^5.5.1", "react-native-screens": "^4.11.1", "react-native-webview": "^13.15.0", "expo-screen-orientation": "~8.1.7"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}