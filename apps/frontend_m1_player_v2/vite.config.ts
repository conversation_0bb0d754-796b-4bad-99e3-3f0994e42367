import { defineConfig, loadEnv } from 'vite';
import path from 'node:path';

export default defineConfig(({ mode }) => {
  const rootEnvDir = path.resolve(__dirname, '../../');
  const env = loadEnv(mode, rootEnvDir, '');
  const defaultFallback = 'http://192.168.2.162:8080';
  const resolvedApi = env.VITE_API_BASE_URL || env.API_BASE_URL || defaultFallback;
  const resolvedDebug = env.DEBUG_MODE ?? (mode !== 'production' ? 'true' : 'false');
  const resolvedNodeEnv = env.NODE_ENV || (mode === 'production' ? 'production' : 'development');

  return {
    envDir: rootEnvDir,
    define: {
      'import.meta.env.VITE_API_BASE_URL': JSON.stringify(resolvedApi),
      'import.meta.env.VITE_DEBUG_MODE': JSON.stringify(String(resolvedDebug)),
      'import.meta.env.VITE_NODE_ENV': JSON.stringify(resolvedNodeEnv),
    },
  };
}); 