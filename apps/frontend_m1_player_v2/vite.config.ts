import { defineConfig, loadEnv } from 'vite';
import path from 'node:path';

export default defineConfig(({ mode }) => {
  const rootEnvDir = path.resolve(__dirname, '../../');
  // 读取根目录环境变量（不限制前缀，便于复用 API_BASE_URL）
  const env = loadEnv(mode, rootEnvDir, '');
  const defaultFallback = 'http://192.168.2.159:8080';
  const resolvedApi = env.VITE_API_BASE_URL || env.API_BASE_URL || defaultFallback;

  return {
    envDir: rootEnvDir,
    define: {
      'import.meta.env.VITE_API_BASE_URL': JSON.stringify(resolvedApi),
    },
  };
}); 