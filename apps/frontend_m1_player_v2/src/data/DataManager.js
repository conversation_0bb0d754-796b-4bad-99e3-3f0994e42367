import { apiService } from '../services/ApiService.js';

export class DataManager {
  constructor(initialVideos = [], requestNextPageFn) {
    this.videos = [...initialVideos];
    this.requestNextPageFn = requestNextPageFn;

    this.isLoading = false;
    // Assume there is more data if a function to request it is provided
    this.hasMoreData = typeof this.requestNextPageFn === 'function';

    // User states cache
    this.userStatesCache = new Map();
    this.userStatesLoaded = false;
  }

  /**
   * Gets video data for a specific index.
   * @param {number} index The index of the video to get.
   * @returns {object | null} The video data object or null if not found.
   */
  getVideo(index) {
    if (index < 0 || index >= this.videos.length) {
      return null;
    }
    return this.videos[index];
  }

  /**
   * Requests more videos using the provided function.
   * @returns {Promise<boolean>} A promise that resolves to true if new videos were added, false otherwise.
   */
  async requestMoreVideos() {
    if (this.isLoading || !this.hasMoreData) {
      return false;
    }

    this.isLoading = true;
    console.log('Requesting next page of videos...');

    try {
      const newVideos = await this.requestNextPageFn();
      if (newVideos && newVideos.length > 0) {
        this.videos.push(...newVideos);
        console.log(`Loaded ${newVideos.length} new videos.`);
        return true;
      } else {
        console.log('No more videos to load.');
        this.hasMoreData = false;
        return false;
      }
    } catch (error) {
      console.error('Failed to request more videos:', error);
      return false;
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Gets the total number of videos currently loaded.
   * @returns {number}
   */
  getTotalVideos() {
    return this.videos.length;
  }

  /**
   * Load user states for all videos
   * @returns {Promise<void>}
   */
  async loadUserStates() {
    if (this.videos.length === 0) {
      return;
    }

    try {
      console.log('DataManager: Loading user states...');

      const videoIds = this.videos.map(video => video.id);
      const states = await apiService.getUserStates(videoIds);

      // Update video user states
      states.forEach(state => {
        const video = this.videos.find(v => v.id === state.videoId);
        if (video) {
          video.userState = {
            isLiked: state.liked || false,
            isCollected: state.favorited || false,
            isBlocked: state.blocked || false,
            progressSeconds: state.progressSeconds || 0,
          };

          // Cache the state
          this.userStatesCache.set(state.videoId, video.userState);
        }
      });

      this.userStatesLoaded = true;
      console.log(`DataManager: User states loaded for ${states.length} videos`);

    } catch (error) {
      console.error('DataManager: Failed to load user states:', error);
    }
  }

  /**
   * Update video user state and sync with backend
   * @param {string} videoId - Video ID
   * @param {string} action - Action type ('like', 'collect', etc.)
   * @param {Object} newState - New state data
   * @returns {Promise<boolean>}
   */
  async updateVideoUserState(videoId, action, newState) {
    const video = this.videos.find(v => v.id === videoId);
    if (!video) {
      console.warn('DataManager: Video not found:', videoId);
      return false;
    }

    try {
      // Update local state immediately for responsive UI
      video.userState = {
        ...video.userState,
        ...newState,
      };

      // Cache the updated state
      this.userStatesCache.set(videoId, video.userState);

      // Sync with backend
      const success = await apiService.createInteraction(videoId, action, newState);

      if (success) {
        console.log(`DataManager: ${action} interaction created for video ${videoId}`);
      } else {
        console.warn(`DataManager: Failed to create ${action} interaction for video ${videoId}`);
      }

      return success;

    } catch (error) {
      console.error('DataManager: Failed to update video user state:', error);
      return false;
    }
  }

  /**
   * Toggle video like state
   * @param {string} videoId - Video ID
   * @returns {Promise<boolean>} New like state
   */
  async toggleVideoLike(videoId) {
    const video = this.videos.find(v => v.id === videoId);
    if (!video) return false;

    const newLikedState = !video.userState.isLiked;

    // Update stats optimistically
    if (newLikedState) {
      const currentLikes = parseInt(video.stats.likes.replace(/[^0-9]/g, ''), 10) || 0;
      video.stats.likes = (currentLikes + 1).toLocaleString();
    } else {
      const currentLikes = parseInt(video.stats.likes.replace(/[^0-9]/g, ''), 10) || 0;
      video.stats.likes = Math.max(0, currentLikes - 1).toLocaleString();
    }

    await this.updateVideoUserState(videoId, 'like', { liked: newLikedState });
    return newLikedState;
  }

  /**
   * Toggle video collection state
   * @param {string} videoId - Video ID
   * @returns {Promise<boolean>} New collection state
   */
  async toggleVideoCollection(videoId) {
    const video = this.videos.find(v => v.id === videoId);
    if (!video) return false;

    const newCollectedState = !video.userState.isCollected;

    // Update stats optimistically
    if (newCollectedState) {
      const currentCollections = parseInt(video.stats.collections.replace(/[^0-9]/g, ''), 10) || 0;
      video.stats.collections = (currentCollections + 1).toLocaleString();
    } else {
      const currentCollections = parseInt(video.stats.collections.replace(/[^0-9]/g, ''), 10) || 0;
      video.stats.collections = Math.max(0, currentCollections - 1).toLocaleString();
    }

    await this.updateVideoUserState(videoId, 'favorite', { favorited: newCollectedState });
    return newCollectedState;
  }

  /**
   * Update video progress
   * @param {string} videoId - Video ID
   * @param {number} progressSeconds - Progress in seconds
   */
  async updateVideoProgress(videoId, progressSeconds) {
    const video = this.videos.find(v => v.id === videoId);
    if (!video) return;

    video.userState.progressSeconds = progressSeconds;
    this.userStatesCache.set(videoId, video.userState);

    // Throttle progress updates to backend (every 10 seconds)
    if (!video._lastProgressUpdate ||
        Date.now() - video._lastProgressUpdate > 10000) {

      video._lastProgressUpdate = Date.now();
      await this.updateVideoUserState(videoId, 'view_progress', {
        progressSeconds
      });
    }
  }

  /**
   * Get cached user state for a video
   * @param {string} videoId - Video ID
   * @returns {Object|null}
   */
  getCachedUserState(videoId) {
    return this.userStatesCache.get(videoId) || null;
  }

  /**
   * Clear user states cache
   */
  clearUserStatesCache() {
    this.userStatesCache.clear();
    this.userStatesLoaded = false;
  }
}